import cookieParser from "cookie-parser";
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import helmet from "helmet";
import { createServer } from "http";
import morgan from "morgan";

// Load environment variables
dotenv.config();

// Import configurations and middleware
import { corsOptions } from "./config/cors";
import { errorHandler } from "./middleware/errorHandler";
import { notFoundHandler } from "./middleware/notFoundHandler";
import { rateLimiter } from "./middleware/rateLimiter";
// import { enhancedRateLimiter, progressiveSlowDown, initializeRateLimiting } from "./middleware/distributedRateLimiter";
// import { initializeRedis } from "./config/redis";
// import { DistributedCacheService } from "./services/distributedCacheService";
import { Logger } from "./services/loggerService";
// import HealthCheckService from "./services/healthCheckService";

// Import routes
import authRoutes from "./routes/auth";
import userRoutes from "./routes/user";
// import monitoringRoutes from "./routes/monitoring";

const app = express();
const PORT = parseInt(process.env.PORT || "3000");

// Initialize services (Redis disabled for now)
const initializeServices = async () => {
  try {
    // if (process.env.ENABLE_DISTRIBUTED_CACHE === "true") {
    //   await initializeRedis();
    //   DistributedCacheService.init();
    // }
    // initializeRateLimiting();
    // HealthCheckService.init();
    Logger.info("Basic services initialized successfully");
  } catch (error) {
    Logger.error("Failed to initialize services:", error);
    process.exit(1);
  }
};

// Create HTTP server with enhanced configuration
const server = createServer(app);

// Configure server for high concurrency
server.keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT || "65000");
server.headersTimeout = parseInt(process.env.HEADERS_TIMEOUT || "66000");

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: process.env.NODE_ENV === "production",
  })
);

// Response compression removed - package was uninstalled

// CORS configuration
app.use(cors(corsOptions));

// Body parsing middleware (optimized for performance)
app.use(
  express.json({
    limit: "10mb",
    strict: true,
    type: ["application/json", "application/*+json"],
  })
);
app.use(
  express.urlencoded({
    extended: true,
    limit: "10mb",
    parameterLimit: 1000,
  })
);

// Cookie parser
app.use(cookieParser());

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Rate limiting (using existing implementation for now)
app.use(rateLimiter);

// Performance monitoring middleware removed for simplicity

// API optimization middleware removed for simplicity

// Quick health check endpoint (basic version)
app.get("/health", (_req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Alumni Portal API is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
  });
});

// Readiness check (for Kubernetes)
app.get("/ready", (_req, res) => {
  res.status(200).json({
    status: "ready",
    timestamp: new Date().toISOString(),
  });
});

// Liveness check (for Kubernetes)
app.get("/live", (_req, res) => {
  res.status(200).json({
    status: "alive",
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
  });
});

// !API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
// app.use("/api/monitoring", monitoringRoutes);

// 404 handler Middleware
app.use(notFoundHandler);

// Global error handler Middleware
app.use(errorHandler);

// Start HTTP server with service initialization
const startServer = async () => {
  try {
    // Initialize services first
    await initializeServices();

    // Start server
    server.listen(PORT, () => {
      console.log(`🚀 Alumni Portal API server running on port http://localhost:${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/health`);
      console.log(`⚡ Enhanced for 10K+ concurrent users`);
      console.log(
        `🔄 Redis distributed caching: ${process.env.ENABLE_DISTRIBUTED_CACHE === "true" ? "Enabled" : "Disabled"}`
      );
      console.log(`📊 Clustering: ${process.env.CLUSTER_ENABLED === "true" ? "Enabled" : "Disabled"}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

startServer();

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    process.exit(0);
  });
});

export default app;
