import { Request, Response, NextFunction } from "express";
import rateLimit, { Store } from "express-rate-limit";
import slowDown from "express-slow-down";
import { RedisService } from "../config/redis";
import { Logger } from "../services/loggerService";
import { CacheKeys } from "../services/distributedCacheService";

// Rate limiting configuration for 10K+ users
const RATE_LIMIT_CONFIG = {
  useDistributed: process.env.ENABLE_DISTRIBUTED_RATE_LIMITING === "true",
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
  maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "500"), // Increased for 10K users
  authMaxRequests: parseInt(process.env.RATE_LIMIT_AUTH_MAX_REQUESTS || "10"),
  skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === "true",
  skipFailedRequests: process.env.RATE_LIMIT_SKIP_FAILED_REQUESTS === "true",
};

// Custom Redis store for distributed rate limiting
class RedisRateLimitStore implements Store {
  private windowMs: number;
  private keyPrefix: string;

  constructor(windowMs: number, keyPrefix: string = "rl:") {
    this.windowMs = windowMs;
    this.keyPrefix = keyPrefix;
  }

  async increment(key: string): Promise<{ totalHits: number; timeToExpire?: number }> {
    const redisKey = `${this.keyPrefix}${key}`;

    try {
      const current = await RedisService.incr(redisKey);

      if (current === 1) {
        // First request in window, set expiration
        await RedisService.expire(redisKey, Math.ceil(this.windowMs / 1000));
        return { totalHits: current, timeToExpire: this.windowMs };
      }

      return { totalHits: current || 0 };
    } catch (error) {
      Logger.error(`Redis rate limit store error for key ${key}:`, error);
      // Fallback: allow request if Redis is down
      return { totalHits: 0 };
    }
  }

  async decrement(key: string): Promise<void> {
    const redisKey = `${this.keyPrefix}${key}`;

    try {
      const current = await RedisService.get(redisKey);
      if (current && parseInt(current) > 0) {
        await RedisService.set(redisKey, (parseInt(current) - 1).toString());
      }
    } catch (error) {
      Logger.error(`Redis rate limit decrement error for key ${key}:`, error);
    }
  }

  async resetKey(key: string): Promise<void> {
    const redisKey = `${this.keyPrefix}${key}`;

    try {
      await RedisService.del(redisKey);
    } catch (error) {
      Logger.error(`Redis rate limit reset error for key ${key}:`, error);
    }
  }
}

// Create distributed rate limit store
const createRateLimitStore = (windowMs: number, keyPrefix?: string): Store | undefined => {
  if (RATE_LIMIT_CONFIG.useDistributed) {
    return new RedisRateLimitStore(windowMs, keyPrefix);
  }
  // Fallback to memory store (from express-rate-limit)
  return undefined; // Uses default memory store
};

// Enhanced rate limiter with better performance for 10K users
export const enhancedRateLimiter = rateLimit({
  windowMs: RATE_LIMIT_CONFIG.windowMs,
  max: RATE_LIMIT_CONFIG.maxRequests,
  message: {
    error: "Too many requests from this IP, please try again later.",
    retryAfter: Math.ceil(RATE_LIMIT_CONFIG.windowMs / 1000),
    limit: RATE_LIMIT_CONFIG.maxRequests,
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: RATE_LIMIT_CONFIG.skipSuccessfulRequests,
  skipFailedRequests: RATE_LIMIT_CONFIG.skipFailedRequests,
  store: createRateLimitStore(RATE_LIMIT_CONFIG.windowMs, "general:"),
  keyGenerator: (req: Request) => {
    // Use user ID if authenticated, otherwise IP
    return req.user?.userId || req.ip || "unknown";
  },
  handler: (req: Request, res: Response) => {
    Logger.security("Rate limit exceeded", req.ip, {
      userAgent: req.get("User-Agent"),
      endpoint: req.path,
      method: req.method,
      userId: req.user?.userId,
    });

    res.status(429).json({
      error: "Too many requests, please try again later.",
      retryAfter: Math.ceil(RATE_LIMIT_CONFIG.windowMs / 1000),
      limit: RATE_LIMIT_CONFIG.maxRequests,
    });
  },
  skip: (req: Request) => {
    // Skip rate limiting for health checks and internal requests
    return req.path === "/health" || req.path === "/metrics";
  },
});

// Stricter rate limiting for authentication endpoints
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: RATE_LIMIT_CONFIG.authMaxRequests,
  message: {
    error: "Too many authentication attempts, please try again later.",
    retryAfter: 900,
    limit: RATE_LIMIT_CONFIG.authMaxRequests,
  },
  standardHeaders: true,
  legacyHeaders: false,
  store: createRateLimitStore(15 * 60 * 1000, "auth:"),
  keyGenerator: (req: Request) => {
    // For auth, always use IP to prevent account enumeration
    return req.ip || "unknown";
  },
  handler: (req: Request, res: Response) => {
    Logger.security("Auth rate limit exceeded", req.ip, {
      userAgent: req.get("User-Agent"),
      endpoint: req.path,
      method: req.method,
      email: req.body?.email, // Log attempted email for security monitoring
    });

    res.status(429).json({
      error: "Too many authentication attempts, please try again later.",
      retryAfter: 900,
      limit: RATE_LIMIT_CONFIG.authMaxRequests,
    });
  },
});

// Progressive delay for repeated requests (DDoS protection)
export const progressiveSlowDown = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: Math.floor(RATE_LIMIT_CONFIG.maxRequests * 0.5), // Start slowing down after 50% of limit
  delayMs: 500, // Start with 500ms delay
  maxDelayMs: 20000, // Maximum 20 second delay
  skipFailedRequests: true,
  skipSuccessfulRequests: false,
  keyGenerator: (req: Request) => {
    return req.user?.userId || req.ip || "unknown";
  },
});

// Custom rate limiter for specific endpoints
export const createCustomRateLimiter = (options: {
  windowMs: number;
  max: number;
  keyPrefix: string;
  skipCondition?: (req: Request) => boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    store: createRateLimitStore(options.windowMs, options.keyPrefix),
    keyGenerator: (req: Request) => {
      return req.user?.userId || req.ip || "unknown";
    },
    skip: options.skipCondition || (() => false),
    handler: (req: Request, res: Response) => {
      Logger.security(`Custom rate limit exceeded (${options.keyPrefix})`, req.ip, {
        userAgent: req.get("User-Agent"),
        endpoint: req.path,
        method: req.method,
        userId: req.user?.userId,
      });

      res.status(429).json({
        error: "Rate limit exceeded for this operation.",
        retryAfter: Math.ceil(options.windowMs / 1000),
        limit: options.max,
      });
    },
  });
};

// WebSocket connection rate limiter
export const wsConnectionLimiter = async (ip: string): Promise<boolean> => {
  const key = CacheKeys.rateLimit(ip, "ws:connect");
  const windowMs = 60000; // 1 minute
  const maxConnections = 10; // Max 10 WS connections per minute per IP

  try {
    if (RATE_LIMIT_CONFIG.useDistributed) {
      const current = await RedisService.incr(key);

      if (current === 1) {
        await RedisService.expire(key, Math.ceil(windowMs / 1000));
      }

      if (current && current > maxConnections) {
        Logger.security("WebSocket connection rate limit exceeded", ip);
        return false;
      }
    }

    return true;
  } catch (error) {
    Logger.error("WebSocket rate limit check error:", error);
    // Allow connection if rate limiting fails
    return true;
  }
};

// API endpoint specific rate limiters
export const apiRateLimiters = {
  // File upload rate limiter
  fileUpload: createCustomRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 5, // 5 uploads per minute
    keyPrefix: "upload:",
  }),

  // Search rate limiter
  search: createCustomRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 30, // 30 searches per minute
    keyPrefix: "search:",
  }),

  // Message sending rate limiter
  messaging: createCustomRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 20, // 20 messages per minute
    keyPrefix: "message:",
  }),

  // Post creation rate limiter
  posting: createCustomRateLimiter({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 10, // 10 posts per 5 minutes
    keyPrefix: "post:",
  }),
};

// Rate limit status checker
export const getRateLimitStatus = async (
  key: string,
  windowMs: number
): Promise<{
  remaining: number;
  resetTime: Date;
  total: number;
}> => {
  try {
    if (RATE_LIMIT_CONFIG.useDistributed) {
      const redisKey = `rl:${key}`;
      const current = await RedisService.get(redisKey);
      const currentCount = current ? parseInt(current) : 0;

      return {
        remaining: Math.max(0, RATE_LIMIT_CONFIG.maxRequests - currentCount),
        resetTime: new Date(Date.now() + windowMs),
        total: RATE_LIMIT_CONFIG.maxRequests,
      };
    }

    // Fallback for memory store (limited functionality)
    return {
      remaining: RATE_LIMIT_CONFIG.maxRequests,
      resetTime: new Date(Date.now() + windowMs),
      total: RATE_LIMIT_CONFIG.maxRequests,
    };
  } catch (error) {
    Logger.error("Rate limit status check error:", error);
    return {
      remaining: RATE_LIMIT_CONFIG.maxRequests,
      resetTime: new Date(Date.now() + windowMs),
      total: RATE_LIMIT_CONFIG.maxRequests,
    };
  }
};

// Initialize rate limiting
export const initializeRateLimiting = (): void => {
  Logger.info("Rate limiting initialized", {
    useDistributed: RATE_LIMIT_CONFIG.useDistributed,
    windowMs: RATE_LIMIT_CONFIG.windowMs,
    maxRequests: RATE_LIMIT_CONFIG.maxRequests,
    authMaxRequests: RATE_LIMIT_CONFIG.authMaxRequests,
  });
};

export default {
  enhancedRateLimiter,
  authRateLimiter,
  progressiveSlowDown,
  createCustomRateLimiter,
  wsConnectionLimiter,
  apiRateLimiters,
  getRateLimitStatus,
  initializeRateLimiting,
};
