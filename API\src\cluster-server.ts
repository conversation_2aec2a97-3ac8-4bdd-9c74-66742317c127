#!/usr/bin/env node

/**
 * Production-ready cluster server for 10K+ concurrent users
 * This file should be used as the main entry point in production
 */

import cluster from "node:cluster";
import * as os from "os";
import { gracefulDatabaseShutdown } from "./config/database";
import { gracefulRedisShutdown } from "./config/redis";
import { Logger } from "./services/loggerService";

// Cluster configuration optimized for high load
const CLUSTER_CONFIG = {
  // Number of workers (default to CPU count, but allow override)
  workers: parseInt(process.env.CLUSTER_WORKERS || os.cpus().length.toString()),
  // Enable clustering (can be disabled for development)
  enabled: process.env.CLUSTER_ENABLED !== "false",
  // Restart delay for failed workers
  restartDelay: parseInt(process.env.CLUSTER_RESTART_DELAY || "1000"),
  // Maximum restart attempts per worker
  maxRestarts: parseInt(process.env.CLUSTER_MAX_RESTARTS || "5"),
  // Grace period for worker shutdown
  shutdownTimeout: parseInt(process.env.CLUSTER_SHUTDOWN_TIMEOUT || "10000"),
  // Memory threshold for worker restart (in MB)
  memoryThreshold: parseInt(process.env.CLUSTER_MEMORY_THRESHOLD || "512"),
  // CPU threshold for worker restart (percentage)
  cpuThreshold: parseInt(process.env.CLUSTER_CPU_THRESHOLD || "90"),
};

// Worker restart tracking
const workerRestarts = new Map<number, number>();
const workerStats = new Map<number, { memory: number; cpu: number; requests: number }>();

export class ProductionClusterManager {
  private static shutdownInProgress = false;
  private static healthCheckInterval?: NodeJS.Timeout;
  private static statsInterval?: NodeJS.Timeout;

  /**
   * Initialize production cluster
   */
  static init(): boolean {
    // Skip clustering in development or if disabled
    if (process.env.NODE_ENV === "development" || !CLUSTER_CONFIG.enabled) {
      Logger.startup("Clustering disabled, running in single process mode");
      this.startWorker();
      return false;
    }

    if (cluster.isPrimary) {
      this.setupMaster();
      return true;
    } else {
      this.startWorker();
      return false;
    }
  }

  /**
   * Setup master process with enhanced monitoring
   */
  private static setupMaster(): void {
    Logger.startup(`Master process ${process.pid} starting with ${CLUSTER_CONFIG.workers} workers`);

    // Set process title for easier identification
    process.title = "ionalumni-master";

    // Set up cluster settings
    cluster.setupPrimary({
      exec: __filename.replace("cluster-server", "app"),
      silent: false,
    });

    // Fork workers
    for (let i = 0; i < CLUSTER_CONFIG.workers; i++) {
      this.forkWorker();
    }

    // Handle worker events
    this.setupWorkerEventHandlers();

    // Handle master process signals
    this.setupMasterSignalHandlers();

    // Start health monitoring
    this.startHealthMonitoring();

    // Start statistics collection
    this.startStatsCollection();

    // Log cluster status
    this.logClusterStatus();

    Logger.startup("Production cluster master initialized successfully");
  }

  /**
   * Fork a new worker with enhanced configuration
   */
  private static forkWorker(): void {
    const worker = cluster.fork({
      ...process.env,
      WORKER_ID: Object.keys(cluster.workers || {}).length.toString(),
    });

    // Set worker title
    worker.process.title = `ionalumni-worker-${worker.id}`;

    // Initialize worker stats
    workerStats.set(worker.id, { memory: 0, cpu: 0, requests: 0 });

    Logger.info(`Worker ${worker.process.pid} forked with ID ${worker.id}`);
  }

  /**
   * Setup worker event handlers with enhanced monitoring
   */
  private static setupWorkerEventHandlers(): void {
    cluster.on("online", (worker) => {
      Logger.info(`Worker ${worker.process.pid} is online`);
    });

    cluster.on("listening", (worker, address) => {
      Logger.info(`Worker ${worker.process.pid} listening on ${address.address}:${address.port}`);
    });

    cluster.on("exit", (worker, code, signal) => {
      if (this.shutdownInProgress) {
        Logger.info(`Worker ${worker.process.pid} exited during shutdown`);
        return;
      }

      Logger.error(`Worker ${worker.process.pid} died`, {
        code,
        signal,
        workerId: worker.id,
      });

      // Clean up worker stats
      workerStats.delete(worker.id);

      // Check restart attempts
      const restartCount = workerRestarts.get(worker.id) || 0;

      if (restartCount < CLUSTER_CONFIG.maxRestarts) {
        workerRestarts.set(worker.id, restartCount + 1);

        Logger.info(
          `Restarting worker ${worker.process.pid} (attempt ${restartCount + 1}/${CLUSTER_CONFIG.maxRestarts})`
        );

        // Restart after delay
        setTimeout(() => {
          this.forkWorker();
        }, CLUSTER_CONFIG.restartDelay);
      } else {
        Logger.error(`Worker ${worker.process.pid} exceeded maximum restart attempts`);

        // If we lose too many workers, exit master
        const aliveWorkers = Object.keys(cluster.workers || {}).length;
        if (aliveWorkers < Math.ceil(CLUSTER_CONFIG.workers / 2)) {
          Logger.error("Too many workers failed, shutting down master");
          process.exit(1);
        }
      }
    });

    cluster.on("disconnect", (worker) => {
      Logger.warn(`Worker ${worker.process.pid} disconnected`);
    });
  }

  /**
   * Setup master signal handlers for graceful shutdown
   */
  private static setupMasterSignalHandlers(): void {
    const gracefulShutdown = async (signal: string) => {
      if (this.shutdownInProgress) {
        Logger.warn(`Received ${signal} during shutdown, forcing exit`);
        process.exit(1);
      }

      this.shutdownInProgress = true;
      Logger.info(`Received ${signal}, initiating graceful shutdown...`);

      // Clear intervals
      if (this.healthCheckInterval) clearInterval(this.healthCheckInterval);
      if (this.statsInterval) clearInterval(this.statsInterval);

      try {
        // Shutdown all workers gracefully
        await this.shutdownWorkers();

        // Close shared resources
        await this.closeSharedResources();

        Logger.info("Graceful shutdown completed");
        process.exit(0);
      } catch (error) {
        Logger.error("Error during graceful shutdown:", error);
        process.exit(1);
      }
    };

    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Handle uncaught exceptions in master
    process.on("uncaughtException", (error) => {
      Logger.error("Uncaught exception in master process:", error);
      gracefulShutdown("UNCAUGHT_EXCEPTION");
    });

    process.on("unhandledRejection", (reason, promise) => {
      Logger.error("Unhandled rejection in master process:", { reason, promise });
      gracefulShutdown("UNHANDLED_REJECTION");
    });
  }

  /**
   * Start health monitoring for workers
   */
  private static startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(() => {
      this.checkWorkerHealth();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Start statistics collection
   */
  private static startStatsCollection(): void {
    this.statsInterval = setInterval(() => {
      this.collectClusterStats();
    }, 60000); // Collect every minute
  }

  /**
   * Check worker health and restart if necessary
   */
  private static checkWorkerHealth(): void {
    const workers = cluster.workers || {};

    Object.values(workers).forEach((worker) => {
      if (!worker) return;

      // Check if worker is responsive
      const timeout = setTimeout(() => {
        Logger.warn(`Worker ${worker.process.pid} is unresponsive, restarting...`);
        worker.kill();
      }, 5000);

      worker.send({ type: "health-check" }, (error) => {
        clearTimeout(timeout);
        if (error) {
          Logger.error(`Health check failed for worker ${worker.process.pid}:`, error);
        }
      });
    });
  }

  /**
   * Collect cluster statistics
   */
  private static collectClusterStats(): void {
    const workers = cluster.workers || {};
    const aliveWorkers = Object.keys(workers).length;
    const totalMemory = process.memoryUsage();

    Logger.info("Cluster statistics", {
      totalWorkers: CLUSTER_CONFIG.workers,
      aliveWorkers,
      masterMemory: {
        rss: Math.round(totalMemory.rss / 1024 / 1024) + "MB",
        heapUsed: Math.round(totalMemory.heapUsed / 1024 / 1024) + "MB",
      },
      uptime: Math.round(process.uptime()) + "s",
    });
  }

  /**
   * Shutdown all workers gracefully
   */
  private static async shutdownWorkers(): Promise<void> {
    const workers = cluster.workers || {};
    const shutdownPromises: Promise<void>[] = [];

    Object.values(workers).forEach((worker) => {
      if (!worker) return;

      shutdownPromises.push(
        new Promise((resolve) => {
          const timeout = setTimeout(() => {
            Logger.warn(`Force killing worker ${worker.process.pid}`);
            worker.kill("SIGKILL");
            resolve();
          }, CLUSTER_CONFIG.shutdownTimeout);

          worker.on("exit", () => {
            clearTimeout(timeout);
            resolve();
          });

          worker.disconnect();
        })
      );
    });

    await Promise.all(shutdownPromises);
    Logger.info("All workers shut down");
  }

  /**
   * Close shared resources
   */
  private static async closeSharedResources(): Promise<void> {
    try {
      await Promise.all([gracefulDatabaseShutdown(), gracefulRedisShutdown()]);
    } catch (error) {
      Logger.error("Error closing shared resources:", error);
    }
  }

  /**
   * Start worker process
   */
  private static startWorker(): void {
    // Set worker process title
    process.title = `ionalumni-worker-${process.pid}`;

    // Handle worker-specific messages
    process.on("message", (message: any) => {
      if (message.type === "health-check") {
        // Respond to health check
        process.send?.({ type: "health-response", pid: process.pid });
      }
    });

    // Import and start the main application
    require("./app");

    Logger.info(`Worker ${process.pid} started`);
  }

  /**
   * Log cluster status
   */
  private static logClusterStatus(): void {
    const workers = cluster.workers || {};
    Logger.info("Cluster status", {
      masterPid: process.pid,
      totalWorkers: CLUSTER_CONFIG.workers,
      aliveWorkers: Object.keys(workers).length,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: {
        total: Math.round(os.totalmem() / 1024 / 1024 / 1024) + "GB",
        free: Math.round(os.freemem() / 1024 / 1024 / 1024) + "GB",
      },
      cpus: os.cpus().length,
    });
  }

  /**
   * Get cluster information
   */
  static getClusterInfo() {
    if (!cluster.isPrimary) {
      return {
        isMaster: false,
        workerId: cluster.worker?.id,
        workerPid: process.pid,
        uptime: process.uptime(),
      };
    }

    const workers = Object.values(cluster.workers || {});
    return {
      isMaster: true,
      masterPid: process.pid,
      totalWorkers: CLUSTER_CONFIG.workers,
      aliveWorkers: workers.length,
      workerPids: workers.map((w) => w?.process.pid).filter(Boolean),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };
  }
}

// Start the cluster if this file is run directly
if (require.main === module) {
  ProductionClusterManager.init();
}

export default ProductionClusterManager;
