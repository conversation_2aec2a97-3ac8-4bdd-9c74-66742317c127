import { Logger } from "./loggerService";
import { prisma, getDatabaseMetrics } from "../config/database";
import { checkRedisHealth, getRedisMetrics } from "../config/redis";
import { EnhancedQueueService } from "./enhancedQueueService";
import { getWebSocketStats } from "./enhancedWebSocket";
import { DistributedCacheService } from "./distributedCacheService";

// Health check status enum
export enum HealthStatus {
  HEALTHY = "healthy",
  DEGRADED = "degraded",
  UNHEALTHY = "unhealthy",
}

// Health check result interface
interface HealthCheckResult {
  status: HealthStatus;
  message: string;
  responseTime: number;
  timestamp: string;
  details?: any;
}

// Service health check interface
interface ServiceHealth {
  name: string;
  status: HealthStatus;
  checks: Record<string, HealthCheckResult>;
  overallResponseTime: number;
}

// Circuit breaker state
enum CircuitState {
  CLOSED = "closed",
  OPEN = "open",
  HALF_OPEN = "half_open",
}

// Circuit breaker configuration
interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedResponseTime: number;
}

// Circuit breaker class
class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private successCount: number = 0;

  constructor(
    private name: string,
    private config: CircuitBreakerConfig
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (Date.now() - this.lastFailureTime > this.config.recoveryTimeout) {
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
        Logger.info(`Circuit breaker ${this.name} moved to HALF_OPEN`);
      } else {
        throw new Error(`Circuit breaker ${this.name} is OPEN`);
      }
    }

    const startTime = Date.now();
    
    try {
      const result = await operation();
      const responseTime = Date.now() - startTime;

      // Check if response time is acceptable
      if (responseTime > this.config.expectedResponseTime) {
        Logger.warn(`Slow response from ${this.name}: ${responseTime}ms`);
      }

      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successes to close
        this.state = CircuitState.CLOSED;
        Logger.info(`Circuit breaker ${this.name} moved to CLOSED`);
      }
    }
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      Logger.error(`Circuit breaker ${this.name} moved to OPEN after ${this.failures} failures`);
    }
  }

  getState(): CircuitState {
    return this.state;
  }

  getStats() {
    return {
      name: this.name,
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
      successCount: this.successCount,
    };
  }
}

// Health check service
export class HealthCheckService {
  private static circuitBreakers = new Map<string, CircuitBreaker>();
  private static healthHistory: Array<{ timestamp: string; status: HealthStatus; details: any }> = [];
  private static readonly MAX_HISTORY = 100;

  // Initialize circuit breakers
  static init(): void {
    const defaultConfig: CircuitBreakerConfig = {
      failureThreshold: 5,
      recoveryTimeout: 30000, // 30 seconds
      monitoringPeriod: 60000, // 1 minute
      expectedResponseTime: 5000, // 5 seconds
    };

    // Create circuit breakers for critical services
    this.circuitBreakers.set("database", new CircuitBreaker("database", defaultConfig));
    this.circuitBreakers.set("redis", new CircuitBreaker("redis", {
      ...defaultConfig,
      expectedResponseTime: 1000, // Redis should be faster
    }));
    this.circuitBreakers.set("queue", new CircuitBreaker("queue", defaultConfig));

    Logger.info("Health check service initialized with circuit breakers");
  }

  // Perform comprehensive health check
  static async performHealthCheck(): Promise<{
    status: HealthStatus;
    services: Record<string, ServiceHealth>;
    system: any;
    timestamp: string;
  }> {
    const startTime = Date.now();
    const services: Record<string, ServiceHealth> = {};

    // Check all services in parallel
    const [
      databaseHealth,
      redisHealth,
      queueHealth,
      cacheHealth,
      websocketHealth,
    ] = await Promise.allSettled([
      this.checkDatabaseHealth(),
      this.checkRedisHealth(),
      this.checkQueueHealth(),
      this.checkCacheHealth(),
      this.checkWebSocketHealth(),
    ]);

    // Process results
    services.database = this.processHealthResult("database", databaseHealth);
    services.redis = this.processHealthResult("redis", redisHealth);
    services.queue = this.processHealthResult("queue", queueHealth);
    services.cache = this.processHealthResult("cache", cacheHealth);
    services.websocket = this.processHealthResult("websocket", websocketHealth);

    // Determine overall status
    const overallStatus = this.determineOverallStatus(services);

    // Get system information
    const systemInfo = this.getSystemInfo();

    const result = {
      status: overallStatus,
      services,
      system: systemInfo,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
    };

    // Store in history
    this.addToHistory(result);

    return result;
  }

  // Database health check
  private static async checkDatabaseHealth(): Promise<ServiceHealth> {
    const checks: Record<string, HealthCheckResult> = {};
    const startTime = Date.now();

    // Connection check
    checks.connection = await this.executeHealthCheck("database-connection", async () => {
      await prisma.$queryRaw`SELECT 1`;
      return { message: "Database connection successful" };
    });

    // Metrics check
    checks.metrics = await this.executeHealthCheck("database-metrics", async () => {
      const metrics = await getDatabaseMetrics();
      return { message: "Database metrics retrieved", details: metrics };
    });

    // Performance check
    checks.performance = await this.executeHealthCheck("database-performance", async () => {
      const start = Date.now();
      await prisma.user.count();
      const duration = Date.now() - start;
      
      if (duration > 1000) {
        throw new Error(`Slow database query: ${duration}ms`);
      }
      
      return { message: `Query completed in ${duration}ms`, details: { duration } };
    });

    return {
      name: "database",
      status: this.getWorstStatus(Object.values(checks)),
      checks,
      overallResponseTime: Date.now() - startTime,
    };
  }

  // Redis health check
  private static async checkRedisHealth(): Promise<ServiceHealth> {
    const checks: Record<string, HealthCheckResult> = {};
    const startTime = Date.now();

    // Connection check
    checks.connection = await this.executeHealthCheck("redis-connection", async () => {
      const isHealthy = await checkRedisHealth();
      if (!isHealthy) {
        throw new Error("Redis connection failed");
      }
      return { message: "Redis connection successful" };
    });

    // Metrics check
    checks.metrics = await this.executeHealthCheck("redis-metrics", async () => {
      const metrics = await getRedisMetrics();
      return { message: "Redis metrics retrieved", details: metrics };
    });

    // Performance check
    checks.performance = await this.executeHealthCheck("redis-performance", async () => {
      const start = Date.now();
      await DistributedCacheService.set("health-check", "test", 10);
      await DistributedCacheService.get("health-check");
      await DistributedCacheService.del("health-check");
      const duration = Date.now() - start;
      
      if (duration > 500) {
        throw new Error(`Slow Redis operations: ${duration}ms`);
      }
      
      return { message: `Redis operations completed in ${duration}ms`, details: { duration } };
    });

    return {
      name: "redis",
      status: this.getWorstStatus(Object.values(checks)),
      checks,
      overallResponseTime: Date.now() - startTime,
    };
  }

  // Queue health check
  private static async checkQueueHealth(): Promise<ServiceHealth> {
    const checks: Record<string, HealthCheckResult> = {};
    const startTime = Date.now();

    // Queue status check
    checks.status = await this.executeHealthCheck("queue-status", async () => {
      const health = await EnhancedQueueService.healthCheck();
      
      if (health.status !== "healthy") {
        throw new Error(`Queue system is ${health.status}`);
      }
      
      return { message: "All queues are healthy", details: health };
    });

    // Queue stats check
    checks.stats = await this.executeHealthCheck("queue-stats", async () => {
      const stats = await EnhancedQueueService.getQueueStats();
      return { message: "Queue statistics retrieved", details: stats };
    });

    return {
      name: "queue",
      status: this.getWorstStatus(Object.values(checks)),
      checks,
      overallResponseTime: Date.now() - startTime,
    };
  }

  // Cache health check
  private static async checkCacheHealth(): Promise<ServiceHealth> {
    const checks: Record<string, HealthCheckResult> = {};
    const startTime = Date.now();

    // Cache stats check
    checks.stats = await this.executeHealthCheck("cache-stats", async () => {
      const stats = DistributedCacheService.getStats();
      return { message: "Cache statistics retrieved", details: stats };
    });

    // Cache performance check
    checks.performance = await this.executeHealthCheck("cache-performance", async () => {
      const start = Date.now();
      const testKey = `health-check-${Date.now()}`;
      const testValue = { test: true, timestamp: Date.now() };
      
      await DistributedCacheService.set(testKey, testValue, 10);
      const retrieved = await DistributedCacheService.get(testKey);
      await DistributedCacheService.del(testKey);
      
      const duration = Date.now() - start;
      
      if (!retrieved || duration > 100) {
        throw new Error(`Cache performance issue: ${duration}ms`);
      }
      
      return { message: `Cache operations completed in ${duration}ms`, details: { duration } };
    });

    return {
      name: "cache",
      status: this.getWorstStatus(Object.values(checks)),
      checks,
      overallResponseTime: Date.now() - startTime,
    };
  }

  // WebSocket health check
  private static async checkWebSocketHealth(): Promise<ServiceHealth> {
    const checks: Record<string, HealthCheckResult> = {};
    const startTime = Date.now();

    // WebSocket stats check
    checks.stats = await this.executeHealthCheck("websocket-stats", async () => {
      const stats = getWebSocketStats();
      
      // Check if we're approaching connection limits
      if (stats.utilizationPercent > 90) {
        throw new Error(`WebSocket utilization too high: ${stats.utilizationPercent}%`);
      }
      
      return { message: "WebSocket statistics retrieved", details: stats };
    });

    return {
      name: "websocket",
      status: this.getWorstStatus(Object.values(checks)),
      checks,
      overallResponseTime: Date.now() - startTime,
    };
  }

  // Execute health check with circuit breaker
  private static async executeHealthCheck(
    checkName: string,
    operation: () => Promise<any>
  ): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const circuitBreaker = this.circuitBreakers.get(checkName.split("-")[0]);
      
      let result;
      if (circuitBreaker) {
        result = await circuitBreaker.execute(operation);
      } else {
        result = await operation();
      }
      
      return {
        status: HealthStatus.HEALTHY,
        message: result.message || "Check passed",
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
        details: result.details,
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: error instanceof Error ? error.message : String(error),
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Helper methods
  private static processHealthResult(
    serviceName: string,
    result: PromiseSettledResult<ServiceHealth>
  ): ServiceHealth {
    if (result.status === "fulfilled") {
      return result.value;
    } else {
      return {
        name: serviceName,
        status: HealthStatus.UNHEALTHY,
        checks: {
          error: {
            status: HealthStatus.UNHEALTHY,
            message: result.reason?.message || String(result.reason),
            responseTime: 0,
            timestamp: new Date().toISOString(),
          },
        },
        overallResponseTime: 0,
      };
    }
  }

  private static getWorstStatus(checks: HealthCheckResult[]): HealthStatus {
    if (checks.some(check => check.status === HealthStatus.UNHEALTHY)) {
      return HealthStatus.UNHEALTHY;
    }
    if (checks.some(check => check.status === HealthStatus.DEGRADED)) {
      return HealthStatus.DEGRADED;
    }
    return HealthStatus.HEALTHY;
  }

  private static determineOverallStatus(services: Record<string, ServiceHealth>): HealthStatus {
    const statuses = Object.values(services).map(service => service.status);
    
    // Critical services that must be healthy
    const criticalServices = ["database"];
    const criticalStatuses = criticalServices.map(name => services[name]?.status);
    
    if (criticalStatuses.some(status => status === HealthStatus.UNHEALTHY)) {
      return HealthStatus.UNHEALTHY;
    }
    
    return this.getWorstStatus(statuses.map(status => ({ status } as HealthCheckResult)));
  }

  private static getSystemInfo() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
    };
  }

  private static addToHistory(result: any): void {
    this.healthHistory.push({
      timestamp: result.timestamp,
      status: result.status,
      details: {
        responseTime: result.responseTime,
        serviceCount: Object.keys(result.services).length,
        healthyServices: Object.values(result.services).filter(
          (s: any) => s.status === HealthStatus.HEALTHY
        ).length,
      },
    });

    // Keep only recent history
    if (this.healthHistory.length > this.MAX_HISTORY) {
      this.healthHistory = this.healthHistory.slice(-this.MAX_HISTORY);
    }
  }

  // Get circuit breaker status
  static getCircuitBreakerStatus() {
    const status: Record<string, any> = {};
    
    this.circuitBreakers.forEach((breaker, name) => {
      status[name] = breaker.getStats();
    });
    
    return status;
  }

  // Get health history
  static getHealthHistory() {
    return this.healthHistory;
  }

  // Quick health check (for load balancer)
  static async quickHealthCheck(): Promise<{ status: HealthStatus; timestamp: string }> {
    try {
      // Just check if we can connect to database
      await prisma.$queryRaw`SELECT 1`;
      
      return {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

export default HealthCheckService;
