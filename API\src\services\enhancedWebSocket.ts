import { WebSocketServer, WebSocket } from "ws";
import { AuthUtils } from "../utils/auth";
import { prisma } from "../config/database";
import { Logger } from "./loggerService";
import { redisPubSubClient, redisClient } from "../config/redis";
import { wsConnectionLimiter } from "../middleware/distributedRateLimiter";
import { DistributedCacheService, CacheKeys } from "./distributedCacheService";

// Enhanced WebSocket interface with additional properties
interface ExtendedWebSocket extends WebSocket {
  userId?: string;
  userRole?: string;
  isAuthenticated?: boolean;
  lastPing?: number;
  connectionId?: string;
  rooms?: Set<string>;
}

// WebSocket configuration for 10K+ users
const WS_CONFIG = {
  maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS || "10000"),
  heartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL || "30000"),
  connectionTimeout: parseInt(process.env.WS_CONNECTION_TIMEOUT || "60000"),
  enableClustering: process.env.ENABLE_WS_CLUSTERING === "true",
  enableRateLimiting: true,
  maxRoomsPerConnection: 50,
  maxMessageSize: 64 * 1024, // 64KB
};

// Connection management
class ConnectionManager {
  private connections = new Map<string, ExtendedWebSocket>();
  private userConnections = new Map<string, Set<string>>();
  private rooms = new Map<string, Set<string>>();
  private connectionCount = 0;

  // Add connection
  addConnection(ws: ExtendedWebSocket, connectionId: string): boolean {
    if (this.connectionCount >= WS_CONFIG.maxConnections) {
      Logger.warn("WebSocket connection limit reached", { maxConnections: WS_CONFIG.maxConnections });
      return false;
    }

    ws.connectionId = connectionId;
    ws.rooms = new Set();
    ws.lastPing = Date.now();

    this.connections.set(connectionId, ws);
    this.connectionCount++;

    Logger.websocket("Connection added", undefined, { connectionId, totalConnections: this.connectionCount });
    return true;
  }

  // Remove connection
  removeConnection(connectionId: string): void {
    const ws = this.connections.get(connectionId);
    if (!ws) return;

    // Remove from user connections
    if (ws.userId) {
      const userConns = this.userConnections.get(ws.userId);
      if (userConns) {
        userConns.delete(connectionId);
        if (userConns.size === 0) {
          this.userConnections.delete(ws.userId);
        }
      }
    }

    // Remove from rooms
    if (ws.rooms) {
      ws.rooms.forEach(room => {
        this.leaveRoom(connectionId, room);
      });
    }

    this.connections.delete(connectionId);
    this.connectionCount--;

    Logger.websocket("Connection removed", ws.userId, { connectionId, totalConnections: this.connectionCount });
  }

  // Authenticate connection
  authenticateConnection(connectionId: string, userId: string, userRole: string): void {
    const ws = this.connections.get(connectionId);
    if (!ws) return;

    ws.userId = userId;
    ws.userRole = userRole;
    ws.isAuthenticated = true;

    // Add to user connections
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(connectionId);

    Logger.websocket("Connection authenticated", userId, { connectionId, userRole });
  }

  // Join room
  joinRoom(connectionId: string, room: string): boolean {
    const ws = this.connections.get(connectionId);
    if (!ws || !ws.isAuthenticated) return false;

    if (ws.rooms!.size >= WS_CONFIG.maxRoomsPerConnection) {
      Logger.warn("Max rooms per connection reached", ws.userId, { connectionId, maxRooms: WS_CONFIG.maxRoomsPerConnection });
      return false;
    }

    ws.rooms!.add(room);

    if (!this.rooms.has(room)) {
      this.rooms.set(room, new Set());
    }
    this.rooms.get(room)!.add(connectionId);

    Logger.websocket("Joined room", ws.userId, { connectionId, room });
    return true;
  }

  // Leave room
  leaveRoom(connectionId: string, room: string): void {
    const ws = this.connections.get(connectionId);
    if (ws && ws.rooms) {
      ws.rooms.delete(room);
    }

    const roomConnections = this.rooms.get(room);
    if (roomConnections) {
      roomConnections.delete(connectionId);
      if (roomConnections.size === 0) {
        this.rooms.delete(room);
      }
    }

    Logger.websocket("Left room", ws?.userId, { connectionId, room });
  }

  // Get user connections
  getUserConnections(userId: string): ExtendedWebSocket[] {
    const connectionIds = this.userConnections.get(userId);
    if (!connectionIds) return [];

    return Array.from(connectionIds)
      .map(id => this.connections.get(id))
      .filter((ws): ws is ExtendedWebSocket => ws !== undefined);
  }

  // Get room connections
  getRoomConnections(room: string): ExtendedWebSocket[] {
    const connectionIds = this.rooms.get(room);
    if (!connectionIds) return [];

    return Array.from(connectionIds)
      .map(id => this.connections.get(id))
      .filter((ws): ws is ExtendedWebSocket => ws !== undefined);
  }

  // Get connection stats
  getStats() {
    return {
      totalConnections: this.connectionCount,
      authenticatedConnections: this.userConnections.size,
      totalRooms: this.rooms.size,
      maxConnections: WS_CONFIG.maxConnections,
      utilizationPercent: Math.round((this.connectionCount / WS_CONFIG.maxConnections) * 100),
    };
  }

  // Cleanup stale connections
  cleanupStaleConnections(): void {
    const now = Date.now();
    const staleConnections: string[] = [];

    this.connections.forEach((ws, connectionId) => {
      if (ws.readyState !== WebSocket.OPEN || 
          (ws.lastPing && now - ws.lastPing > WS_CONFIG.connectionTimeout)) {
        staleConnections.push(connectionId);
      }
    });

    staleConnections.forEach(connectionId => {
      const ws = this.connections.get(connectionId);
      if (ws) {
        ws.terminate();
        this.removeConnection(connectionId);
      }
    });

    if (staleConnections.length > 0) {
      Logger.websocket("Cleaned up stale connections", undefined, { count: staleConnections.length });
    }
  }
}

// Global connection manager
const connectionManager = new ConnectionManager();

// Redis pub/sub for clustering
class WebSocketCluster {
  private static initialized = false;

  static async initialize(): Promise<void> {
    if (!WS_CONFIG.enableClustering || this.initialized) return;

    try {
      // Subscribe to WebSocket events from other instances
      await redisPubSubClient.subscribe("ws:broadcast", "ws:user", "ws:room");

      redisPubSubClient.on("message", (channel: string, message: string) => {
        try {
          const data = JSON.parse(message);
          this.handleClusterMessage(channel, data);
        } catch (error) {
          Logger.error("WebSocket cluster message parse error:", error);
        }
      });

      this.initialized = true;
      Logger.info("WebSocket clustering initialized");
    } catch (error) {
      Logger.error("Failed to initialize WebSocket clustering:", error);
    }
  }

  private static handleClusterMessage(channel: string, data: any): void {
    switch (channel) {
      case "ws:broadcast":
        this.handleBroadcast(data);
        break;
      case "ws:user":
        this.handleUserMessage(data);
        break;
      case "ws:room":
        this.handleRoomMessage(data);
        break;
    }
  }

  private static handleBroadcast(data: any): void {
    // Broadcast to all connections
    connectionManager.getStats(); // This would broadcast to all in a real implementation
  }

  private static handleUserMessage(data: any): void {
    const { userId, message } = data;
    const connections = connectionManager.getUserConnections(userId);
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    });
  }

  private static handleRoomMessage(data: any): void {
    const { room, message } = data;
    const connections = connectionManager.getRoomConnections(room);
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    });
  }

  // Publish message to cluster
  static async publishToUser(userId: string, message: any): Promise<void> {
    if (!WS_CONFIG.enableClustering) return;

    try {
      await redisClient.publish("ws:user", JSON.stringify({ userId, message }));
    } catch (error) {
      Logger.error("Failed to publish user message to cluster:", error);
    }
  }

  static async publishToRoom(room: string, message: any): Promise<void> {
    if (!WS_CONFIG.enableClustering) return;

    try {
      await redisClient.publish("ws:room", JSON.stringify({ room, message }));
    } catch (error) {
      Logger.error("Failed to publish room message to cluster:", error);
    }
  }

  static async publishBroadcast(message: any): Promise<void> {
    if (!WS_CONFIG.enableClustering) return;

    try {
      await redisClient.publish("ws:broadcast", JSON.stringify(message));
    } catch (error) {
      Logger.error("Failed to publish broadcast message to cluster:", error);
    }
  }
}

// Enhanced WebSocket setup
export const setupEnhancedWebSocket = async (wss: WebSocketServer): Promise<void> => {
  Logger.info("Setting up enhanced WebSocket server...", WS_CONFIG);

  // Initialize clustering if enabled
  await WebSocketCluster.initialize();

  // Set up heartbeat interval
  const heartbeatInterval = setInterval(() => {
    connectionManager.cleanupStaleConnections();
  }, WS_CONFIG.heartbeatInterval);

  wss.on("connection", async (ws: ExtendedWebSocket, req) => {
    const ip = req.socket.remoteAddress || "unknown";
    
    // Rate limiting check
    if (WS_CONFIG.enableRateLimiting && !(await wsConnectionLimiter(ip))) {
      ws.close(1008, "Rate limit exceeded");
      return;
    }

    const connectionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Add connection to manager
    if (!connectionManager.addConnection(ws, connectionId)) {
      ws.close(1013, "Server overloaded");
      return;
    }

    // Set up ping/pong for connection health
    ws.on("pong", () => {
      ws.lastPing = Date.now();
    });

    // Handle messages
    ws.on("message", async (message: Buffer) => {
      try {
        // Check message size
        if (message.length > WS_CONFIG.maxMessageSize) {
          ws.close(1009, "Message too large");
          return;
        }

        const data = JSON.parse(message.toString());
        await handleWebSocketMessage(ws, data);
      } catch (error) {
        Logger.error("WebSocket message error:", error);
        ws.send(JSON.stringify({
          type: "error",
          message: "Invalid message format",
        }));
      }
    });

    // Handle connection close
    ws.on("close", () => {
      connectionManager.removeConnection(connectionId);
    });

    // Handle errors
    ws.on("error", (error) => {
      Logger.error("WebSocket error:", error);
      connectionManager.removeConnection(connectionId);
    });

    // Send welcome message
    ws.send(JSON.stringify({
      type: "connected",
      connectionId,
      timestamp: Date.now(),
    }));
  });

  // Graceful shutdown
  process.on("SIGTERM", () => {
    clearInterval(heartbeatInterval);
    wss.close();
  });

  Logger.info("Enhanced WebSocket server setup completed");
};

// Handle WebSocket messages
async function handleWebSocketMessage(ws: ExtendedWebSocket, data: any): Promise<void> {
  switch (data.type) {
    case "auth":
      await handleAuthentication(ws, data);
      break;
    case "join_room":
      await handleJoinRoom(ws, data);
      break;
    case "leave_room":
      await handleLeaveRoom(ws, data);
      break;
    case "send_message":
      await handleSendMessage(ws, data);
      break;
    case "ping":
      ws.send(JSON.stringify({ type: "pong", timestamp: Date.now() }));
      break;
    default:
      ws.send(JSON.stringify({
        type: "error",
        message: "Unknown message type",
      }));
  }
}

// Authentication handler
async function handleAuthentication(ws: ExtendedWebSocket, data: any): Promise<void> {
  try {
    const { token } = data;
    const decoded = AuthUtils.verifyToken(token);
    
    if (!decoded || !decoded.userId) {
      ws.send(JSON.stringify({
        type: "auth_error",
        message: "Invalid token",
      }));
      return;
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, role: true, status: true },
    });

    if (!user || user.status !== "APPROVED") {
      ws.send(JSON.stringify({
        type: "auth_error",
        message: "User not found or not approved",
      }));
      return;
    }

    connectionManager.authenticateConnection(ws.connectionId!, user.id, user.role);

    ws.send(JSON.stringify({
      type: "auth_success",
      userId: user.id,
      role: user.role,
    }));

  } catch (error) {
    Logger.error("WebSocket authentication error:", error);
    ws.send(JSON.stringify({
      type: "auth_error",
      message: "Authentication failed",
    }));
  }
}

// Room management handlers
async function handleJoinRoom(ws: ExtendedWebSocket, data: any): Promise<void> {
  if (!ws.isAuthenticated) {
    ws.send(JSON.stringify({
      type: "error",
      message: "Not authenticated",
    }));
    return;
  }

  const { room } = data;
  const success = connectionManager.joinRoom(ws.connectionId!, room);

  ws.send(JSON.stringify({
    type: success ? "room_joined" : "room_join_failed",
    room,
  }));
}

async function handleLeaveRoom(ws: ExtendedWebSocket, data: any): Promise<void> {
  if (!ws.isAuthenticated) return;

  const { room } = data;
  connectionManager.leaveRoom(ws.connectionId!, room);

  ws.send(JSON.stringify({
    type: "room_left",
    room,
  }));
}

// Message sending handler
async function handleSendMessage(ws: ExtendedWebSocket, data: any): Promise<void> {
  if (!ws.isAuthenticated) {
    ws.send(JSON.stringify({
      type: "error",
      message: "Not authenticated",
    }));
    return;
  }

  try {
    const { receiverId, content, messageId } = data;

    // Send to local connections
    const receiverConnections = connectionManager.getUserConnections(receiverId);
    const messageData = {
      type: "new_message",
      data: {
        id: messageId,
        senderId: ws.userId,
        content,
        timestamp: new Date().toISOString(),
      },
    };

    receiverConnections.forEach(receiverWs => {
      if (receiverWs.readyState === WebSocket.OPEN) {
        receiverWs.send(JSON.stringify(messageData));
      }
    });

    // Send to cluster if enabled
    await WebSocketCluster.publishToUser(receiverId, messageData);

    ws.send(JSON.stringify({
      type: "message_sent",
      messageId,
    }));

  } catch (error) {
    Logger.error("WebSocket send message error:", error);
    ws.send(JSON.stringify({
      type: "error",
      message: "Failed to send message",
    }));
  }
}

// Public API functions
export function sendRealTimeMessage(receiverId: string, messageData: any): boolean {
  const connections = connectionManager.getUserConnections(receiverId);
  let sent = false;

  connections.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: "new_message",
        data: messageData,
      }));
      sent = true;
    }
  });

  // Also send to cluster
  WebSocketCluster.publishToUser(receiverId, {
    type: "new_message",
    data: messageData,
  });

  return sent;
}

export function sendRealTimeNotification(userId: string, notificationData: any): boolean {
  const connections = connectionManager.getUserConnections(userId);
  let sent = false;

  connections.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: "new_notification",
        data: notificationData,
      }));
      sent = true;
    }
  });

  // Also send to cluster
  WebSocketCluster.publishToUser(userId, {
    type: "new_notification",
    data: notificationData,
  });

  return sent;
}

export function broadcastToRoom(room: string, messageData: any): void {
  const connections = connectionManager.getRoomConnections(room);

  connections.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(messageData));
    }
  });

  // Also send to cluster
  WebSocketCluster.publishToRoom(room, messageData);
}

export function getWebSocketStats() {
  return connectionManager.getStats();
}

export default {
  setupEnhancedWebSocket,
  sendRealTimeMessage,
  sendRealTimeNotification,
  broadcastToRoom,
  getWebSocketStats,
};
