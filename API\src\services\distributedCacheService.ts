import { Logger } from "./loggerService";
import { redisCacheClient, RedisService } from "../config/redis";
import { CacheService as NodeCacheService } from "../config/cache";

// Cache statistics interface
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
}

// Cache configuration
const CACHE_CONFIG = {
  defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL || "3600"), // 1 hour
  useRedis: process.env.ENABLE_DISTRIBUTED_CACHE === "true",
  keyPrefix: process.env.CACHE_KEY_PREFIX || "ionalumni:",
  maxKeyLength: 250,
  compressionThreshold: 1024, // Compress values larger than 1KB
};

export class DistributedCacheService {
  private static stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    hitRate: 0,
  };

  // Initialize the cache service
  static init(): void {
    Logger.info("Distributed cache service initialized", {
      useRedis: CACHE_CONFIG.useRedis,
      defaultTTL: CACHE_CONFIG.defaultTTL,
      keyPrefix: CACHE_CONFIG.keyPrefix,
    });

    // Start periodic stats calculation
    setInterval(() => {
      this.calculateHitRate();
    }, 60000); // Every minute
  }

  // Get data from cache
  static async get<T>(key: string): Promise<T | null> {
    const fullKey = this.buildKey(key);

    try {
      let result: string | null = null;

      if (CACHE_CONFIG.useRedis) {
        result = await RedisService.get(fullKey);
      } else {
        result = NodeCacheService.get<string>(fullKey);
      }

      if (result !== null) {
        this.stats.hits++;
        Logger.cache(`Cache hit for key: ${key}`);
        
        // Try to parse JSON, return as-is if not JSON
        try {
          return JSON.parse(result);
        } catch {
          return result as unknown as T;
        }
      }

      this.stats.misses++;
      Logger.cache(`Cache miss for key: ${key}`);
      return null;
    } catch (error) {
      this.stats.errors++;
      Logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  // Set data in cache
  static async set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.defaultTTL): Promise<boolean> {
    const fullKey = this.buildKey(key);

    try {
      // Serialize data
      const serializedData = typeof data === "string" ? data : JSON.stringify(data);
      
      // Compress if data is large
      const finalData = this.shouldCompress(serializedData) 
        ? await this.compress(serializedData)
        : serializedData;

      let success = false;

      if (CACHE_CONFIG.useRedis) {
        success = await RedisService.set(fullKey, finalData, ttl);
      } else {
        success = NodeCacheService.set(fullKey, finalData, ttl);
      }

      if (success) {
        this.stats.sets++;
        Logger.cache(`Cache set for key: ${key}`, key, { ttl });
      }

      return success;
    } catch (error) {
      this.stats.errors++;
      Logger.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  // Delete data from cache
  static async del(key: string): Promise<boolean> {
    const fullKey = this.buildKey(key);

    try {
      let success = false;

      if (CACHE_CONFIG.useRedis) {
        success = await RedisService.del(fullKey);
      } else {
        success = NodeCacheService.del(fullKey);
      }

      if (success) {
        this.stats.deletes++;
        Logger.cache(`Cache delete for key: ${key}`);
      }

      return success;
    } catch (error) {
      this.stats.errors++;
      Logger.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  // Check if key exists
  static async exists(key: string): Promise<boolean> {
    const fullKey = this.buildKey(key);

    try {
      if (CACHE_CONFIG.useRedis) {
        return await RedisService.exists(fullKey);
      } else {
        return NodeCacheService.get(fullKey) !== null;
      }
    } catch (error) {
      this.stats.errors++;
      Logger.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }

  // Get or set pattern (cache-aside)
  static async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = CACHE_CONFIG.defaultTTL
  ): Promise<T | null> {
    // Try to get from cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch data
    try {
      const data = await fetchFunction();
      if (data !== null && data !== undefined) {
        await this.set(key, data, ttl);
      }
      return data;
    } catch (error) {
      Logger.error(`Cache getOrSet fetch error for key ${key}:`, error);
      return null;
    }
  }

  // Increment counter
  static async incr(key: string, ttl?: number): Promise<number | null> {
    const fullKey = this.buildKey(key);

    try {
      if (CACHE_CONFIG.useRedis) {
        const result = await RedisService.incr(fullKey);
        if (ttl && result === 1) {
          // Set TTL only for the first increment
          await RedisService.expire(fullKey, ttl);
        }
        return result;
      } else {
        // Fallback to node-cache (less efficient for counters)
        const current = NodeCacheService.get<number>(fullKey) || 0;
        const newValue = current + 1;
        NodeCacheService.set(fullKey, newValue, ttl || CACHE_CONFIG.defaultTTL);
        return newValue;
      }
    } catch (error) {
      this.stats.errors++;
      Logger.error(`Cache incr error for key ${key}:`, error);
      return null;
    }
  }

  // Get cache statistics
  static getStats(): CacheStats & { timestamp: string } {
    return {
      ...this.stats,
      timestamp: new Date().toISOString(),
    };
  }

  // Clear all cache (use with caution)
  static async clear(): Promise<boolean> {
    try {
      if (CACHE_CONFIG.useRedis) {
        // Clear only keys with our prefix
        const keys = await redisCacheClient.keys(`${CACHE_CONFIG.keyPrefix}*`);
        if (keys.length > 0) {
          await redisCacheClient.del(...keys);
        }
      } else {
        NodeCacheService.flushAll();
      }

      Logger.info("Cache cleared successfully");
      return true;
    } catch (error) {
      this.stats.errors++;
      Logger.error("Cache clear error:", error);
      return false;
    }
  }

  // Warm up cache with frequently accessed data
  static async warmUp(): Promise<void> {
    if (!process.env.ENABLE_CACHE_PRELOADING) {
      return;
    }

    try {
      Logger.info("Starting cache warm-up...");

      // Add your cache warming logic here
      // Example: Pre-load user counts, popular posts, etc.
      
      Logger.info("Cache warm-up completed");
    } catch (error) {
      Logger.error("Cache warm-up error:", error);
    }
  }

  // Private helper methods
  private static buildKey(key: string): string {
    const fullKey = `${CACHE_CONFIG.keyPrefix}${key}`;
    
    // Ensure key length doesn't exceed Redis limits
    if (fullKey.length > CACHE_CONFIG.maxKeyLength) {
      // Use hash of the key if too long
      const crypto = require("crypto");
      const hash = crypto.createHash("sha256").update(fullKey).digest("hex").substring(0, 32);
      return `${CACHE_CONFIG.keyPrefix}hash:${hash}`;
    }
    
    return fullKey;
  }

  private static shouldCompress(data: string): boolean {
    return data.length > CACHE_CONFIG.compressionThreshold;
  }

  private static async compress(data: string): Promise<string> {
    // Simple compression using gzip (you might want to use a more efficient method)
    const zlib = require("zlib");
    const compressed = zlib.gzipSync(data);
    return `gzip:${compressed.toString("base64")}`;
  }

  private static async decompress(data: string): Promise<string> {
    if (!data.startsWith("gzip:")) {
      return data;
    }
    
    const zlib = require("zlib");
    const compressed = Buffer.from(data.substring(5), "base64");
    return zlib.gunzipSync(compressed).toString();
  }

  private static calculateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }
}

// Cache key builders for different data types
export class CacheKeys {
  static user(userId: string): string {
    return `user:${userId}`;
  }

  static userProfile(userId: string): string {
    return `user:profile:${userId}`;
  }

  static userPosts(userId: string, page: number = 1): string {
    return `user:posts:${userId}:page:${page}`;
  }

  static post(postId: string): string {
    return `post:${postId}`;
  }

  static posts(type: string, page: number = 1): string {
    return `posts:${type}:page:${page}`;
  }

  static messages(userId1: string, userId2: string, page: number = 1): string {
    const sortedIds = [userId1, userId2].sort();
    return `messages:${sortedIds[0]}:${sortedIds[1]}:page:${page}`;
  }

  static userCount(): string {
    return "stats:user:count";
  }

  static postCount(): string {
    return "stats:post:count";
  }

  static rateLimit(ip: string, endpoint: string): string {
    return `ratelimit:${ip}:${endpoint}`;
  }

  static session(sessionId: string): string {
    return `session:${sessionId}`;
  }
}

export default DistributedCacheService;
