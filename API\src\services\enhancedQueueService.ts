import Bull, { Queue, Job, JobOptions } from "bull";
import { Logger } from "./loggerService";
import { redisQueueClient } from "../config/redis";
import { sendEmail } from "./emailService";
import { createNotification } from "./notificationService";
import { uploadToCloudinary } from "./cloudinaryService";

// Queue configuration for 10K+ users
const QUEUE_CONFIG = {
  useRedis: process.env.ENABLE_REDIS_QUEUES === "true",
  concurrency: parseInt(process.env.QUEUE_CONCURRENCY || "10"),
  maxAttempts: parseInt(process.env.QUEUE_MAX_ATTEMPTS || "5"),
  backoffDelay: parseInt(process.env.QUEUE_BACKOFF_DELAY || "2000"),
  removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE || "100"),
  removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL || "200"),
  defaultJobOptions: {
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE || "100"),
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL || "200"),
    attempts: parseInt(process.env.QUEUE_MAX_ATTEMPTS || "5"),
    backoff: {
      type: "exponential",
      delay: parseInt(process.env.QUEUE_BACKOFF_DELAY || "2000"),
    },
  } as JobOptions,
};

// Enhanced queue class with Redis support
class EnhancedQueue {
  private bullQueue?: Queue;
  private name: string;
  private processors: Map<string, (job: Job) => Promise<any>> = new Map();

  constructor(name: string) {
    this.name = name;
    
    if (QUEUE_CONFIG.useRedis) {
      this.initializeBullQueue();
    }
  }

  private initializeBullQueue(): void {
    this.bullQueue = new Bull(this.name, {
      redis: {
        port: redisQueueClient.options.port,
        host: redisQueueClient.options.host,
        password: redisQueueClient.options.password,
        db: redisQueueClient.options.db,
      },
      defaultJobOptions: QUEUE_CONFIG.defaultJobOptions,
    });

    // Set up event listeners
    this.bullQueue.on("completed", (job: Job, result: any) => {
      Logger.info(`Queue ${this.name} job completed`, {
        jobId: job.id,
        jobType: job.name,
        duration: Date.now() - job.processedOn!,
        result: typeof result === "object" ? JSON.stringify(result) : result,
      });
    });

    this.bullQueue.on("failed", (job: Job, err: Error) => {
      Logger.error(`Queue ${this.name} job failed`, {
        jobId: job.id,
        jobType: job.name,
        attempts: job.attemptsMade,
        maxAttempts: job.opts.attempts,
        error: err.message,
        stack: err.stack,
      });
    });

    this.bullQueue.on("stalled", (job: Job) => {
      Logger.warn(`Queue ${this.name} job stalled`, {
        jobId: job.id,
        jobType: job.name,
      });
    });

    // Start processing with configured concurrency
    this.bullQueue.process("*", QUEUE_CONFIG.concurrency, async (job: Job) => {
      const processor = this.processors.get(job.name);
      if (!processor) {
        throw new Error(`No processor found for job type: ${job.name}`);
      }
      return await processor(job);
    });

    Logger.info(`Enhanced queue ${this.name} initialized with Redis`);
  }

  // Add job to queue
  async add(jobType: string, data: any, options: JobOptions = {}): Promise<Job | null> {
    try {
      if (this.bullQueue) {
        const job = await this.bullQueue.add(jobType, data, {
          ...QUEUE_CONFIG.defaultJobOptions,
          ...options,
        });
        
        Logger.debug(`Job added to queue ${this.name}`, {
          jobId: job.id,
          jobType,
          priority: options.priority || 0,
          delay: options.delay || 0,
        });
        
        return job;
      } else {
        // Fallback: process immediately if Redis is not available
        Logger.warn(`Redis queue not available, processing job immediately: ${jobType}`);
        const processor = this.processors.get(jobType);
        if (processor) {
          await processor({ data, name: jobType } as Job);
        }
        return null;
      }
    } catch (error) {
      Logger.error(`Failed to add job to queue ${this.name}:`, error);
      return null;
    }
  }

  // Register job processor
  registerProcessor(jobType: string, processor: (job: Job) => Promise<any>): void {
    this.processors.set(jobType, processor);
    Logger.debug(`Processor registered for job type: ${jobType} in queue: ${this.name}`);
  }

  // Get queue statistics
  async getStats(): Promise<any> {
    if (!this.bullQueue) {
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
      };
    }

    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.bullQueue.getWaiting(),
        this.bullQueue.getActive(),
        this.bullQueue.getCompleted(),
        this.bullQueue.getFailed(),
        this.bullQueue.getDelayed(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      };
    } catch (error) {
      Logger.error(`Failed to get stats for queue ${this.name}:`, error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  // Clean up old jobs
  async cleanup(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    if (!this.bullQueue) return;

    try {
      await this.bullQueue.clean(olderThanMs, "completed");
      await this.bullQueue.clean(olderThanMs, "failed");
      Logger.info(`Queue ${this.name} cleanup completed`);
    } catch (error) {
      Logger.error(`Queue ${this.name} cleanup failed:`, error);
    }
  }

  // Pause queue processing
  async pause(): Promise<void> {
    if (this.bullQueue) {
      await this.bullQueue.pause();
      Logger.info(`Queue ${this.name} paused`);
    }
  }

  // Resume queue processing
  async resume(): Promise<void> {
    if (this.bullQueue) {
      await this.bullQueue.resume();
      Logger.info(`Queue ${this.name} resumed`);
    }
  }

  // Close queue
  async close(): Promise<void> {
    if (this.bullQueue) {
      await this.bullQueue.close();
      Logger.info(`Queue ${this.name} closed`);
    }
  }
}

// Create enhanced queues
export const emailQueue = new EnhancedQueue("email");
export const notificationQueue = new EnhancedQueue("notification");
export const fileProcessingQueue = new EnhancedQueue("fileProcessing");
export const analyticsQueue = new EnhancedQueue("analytics");

// Register processors for email queue
emailQueue.registerProcessor("send-email", async (job: Job) => {
  const { to, subject, html, text } = job.data;
  return await sendEmail({ to, subject, html, text });
});

emailQueue.registerProcessor("send-bulk-email", async (job: Job) => {
  const { recipients, subject, html, text } = job.data;
  const results = [];
  
  for (const recipient of recipients) {
    try {
      await sendEmail({ to: recipient, subject, html, text });
      results.push({ recipient, status: "sent" });
    } catch (error) {
      results.push({ 
        recipient, 
        status: "failed", 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }
  
  return results;
});

// Register processors for notification queue
notificationQueue.registerProcessor("send-notification", async (job: Job) => {
  return await createNotification(job.data);
});

// Register processors for file processing queue
fileProcessingQueue.registerProcessor("upload-file", async (job: Job) => {
  const { filePath, options } = job.data;
  return await uploadToCloudinary(filePath, options);
});

// Register processors for analytics queue
analyticsQueue.registerProcessor("track-event", async (job: Job) => {
  const { event, userId, data } = job.data;
  Logger.analytics(`Event tracked: ${event}`, userId, data);
  return { event, userId, timestamp: new Date().toISOString() };
});

// Enhanced queue service with better API
export class EnhancedQueueService {
  // Email operations
  static async sendEmail(emailData: {
    to: string;
    subject: string;
    html: string;
    text?: string;
  }, priority: number = 0): Promise<Job | null> {
    return await emailQueue.add("send-email", emailData, { priority });
  }

  static async sendBulkEmail(emailData: {
    recipients: string[];
    subject: string;
    html: string;
    text?: string;
  }, priority: number = -5): Promise<Job | null> {
    return await emailQueue.add("send-bulk-email", emailData, { priority });
  }

  // Notification operations
  static async sendNotification(notificationData: any, priority: number = 0): Promise<Job | null> {
    return await notificationQueue.add("send-notification", notificationData, { priority });
  }

  // File processing operations
  static async processFile(fileData: {
    filePath: string;
    options?: any;
  }, priority: number = 0): Promise<Job | null> {
    return await fileProcessingQueue.add("upload-file", fileData, { priority });
  }

  // Analytics operations
  static async trackEvent(eventData: {
    event: string;
    userId: string;
    data?: any;
  }, priority: number = -10): Promise<Job | null> {
    return await analyticsQueue.add("track-event", eventData, { priority });
  }

  // Get comprehensive queue statistics
  static async getQueueStats() {
    const queues = [
      { name: "emailQueue", queue: emailQueue },
      { name: "notificationQueue", queue: notificationQueue },
      { name: "fileProcessingQueue", queue: fileProcessingQueue },
      { name: "analyticsQueue", queue: analyticsQueue },
    ];

    const stats = await Promise.all(
      queues.map(async ({ name, queue }) => ({
        name,
        stats: await queue.getStats(),
      }))
    );

    return {
      queues: stats,
      timestamp: new Date().toISOString(),
      totalJobs: stats.reduce((sum, queue) => {
        const queueStats = queue.stats;
        return sum + (queueStats.waiting || 0) + (queueStats.active || 0) + 
               (queueStats.completed || 0) + (queueStats.failed || 0) + (queueStats.delayed || 0);
      }, 0),
    };
  }

  // Health check for all queues
  static async healthCheck() {
    const queues = [emailQueue, notificationQueue, fileProcessingQueue, analyticsQueue];
    const healthChecks = await Promise.all(
      queues.map(async (queue, index) => {
        try {
          const stats = await queue.getStats();
          return {
            name: ["email", "notification", "fileProcessing", "analytics"][index],
            status: "healthy",
            stats,
          };
        } catch (error) {
          return {
            name: ["email", "notification", "fileProcessing", "analytics"][index],
            status: "unhealthy",
            error: error instanceof Error ? error.message : String(error),
          };
        }
      })
    );

    const overallStatus = healthChecks.every(check => check.status === "healthy") ? "healthy" : "degraded";

    return {
      status: overallStatus,
      queues: healthChecks,
      timestamp: new Date().toISOString(),
    };
  }

  // Cleanup old jobs across all queues
  static async cleanupJobs(olderThanHours: number = 24): Promise<void> {
    const olderThanMs = olderThanHours * 60 * 60 * 1000;
    const queues = [emailQueue, notificationQueue, fileProcessingQueue, analyticsQueue];

    await Promise.all(queues.map(queue => queue.cleanup(olderThanMs)));
    Logger.info("Queue cleanup completed for all queues", { olderThanHours });
  }

  // Graceful shutdown of all queues
  static async shutdown(): Promise<void> {
    Logger.info("Shutting down all queues...");
    const queues = [emailQueue, notificationQueue, fileProcessingQueue, analyticsQueue];
    
    await Promise.all(queues.map(queue => queue.close()));
    Logger.info("All queues shut down successfully");
  }
}

export default EnhancedQueueService;
