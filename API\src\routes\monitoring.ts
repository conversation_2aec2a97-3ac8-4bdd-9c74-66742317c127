import { Router } from "express";
import { MetricsService } from "../services/metricsService";
import HealthCheckService from "../services/healthCheckService";
import { EnhancedQueueService } from "../services/enhancedQueueService";
import { DistributedCacheService } from "../services/distributedCacheService";
import { getWebSocketStats } from "../services/enhancedWebSocket";
import { getRedisMetrics } from "../config/redis";
import { getDatabaseMetrics } from "../config/database";
import ProductionClusterManager from "../cluster-server";
import { authenticate } from "../middleware/auth";
import { createCustomRateLimiter } from "../middleware/distributedRateLimiter";

const router = Router();

// Rate limiting for monitoring endpoints
const monitoringRateLimit = createCustomRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requests per minute
  keyPrefix: "monitoring:",
});

// Apply rate limiting to all monitoring routes
router.use(monitoringRateLimit);

// Metrics endpoint (Prometheus format)
router.get("/metrics", async (req, res) => {
  try {
    const prometheusMetrics = MetricsService.exportPrometheusMetrics();
    res.set("Content-Type", "text/plain");
    res.send(prometheusMetrics);
  } catch (error) {
    res.status(500).json({
      error: "Failed to export metrics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Performance report endpoint (requires authentication)
router.get("/performance", authenticate, async (req, res) => {
  try {
    const report = await MetricsService.getPerformanceReport();
    res.json(report);
  } catch (error) {
    res.status(500).json({
      error: "Failed to generate performance report",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// System statistics endpoint
router.get("/stats", async (req, res) => {
  try {
    const [
      queueStats,
      cacheStats,
      wsStats,
      redisMetrics,
      dbMetrics,
      clusterInfo,
    ] = await Promise.allSettled([
      EnhancedQueueService.getQueueStats(),
      DistributedCacheService.getStats(),
      getWebSocketStats(),
      getRedisMetrics(),
      getDatabaseMetrics(),
      ProductionClusterManager.getClusterInfo(),
    ]);

    const stats = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      queues: queueStats.status === "fulfilled" ? queueStats.value : { error: queueStats.reason },
      cache: cacheStats.status === "fulfilled" ? cacheStats.value : { error: cacheStats.reason },
      websockets: wsStats.status === "fulfilled" ? wsStats.value : { error: wsStats.reason },
      redis: redisMetrics.status === "fulfilled" ? redisMetrics.value : { error: redisMetrics.reason },
      database: dbMetrics.status === "fulfilled" ? dbMetrics.value : { error: dbMetrics.reason },
      cluster: clusterInfo.status === "fulfilled" ? clusterInfo.value : { error: clusterInfo.reason },
    };

    res.json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to collect system statistics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Circuit breaker status endpoint
router.get("/circuit-breakers", authenticate, async (req, res) => {
  try {
    const circuitBreakers = HealthCheckService.getCircuitBreakerStatus();
    res.json({
      timestamp: new Date().toISOString(),
      circuitBreakers,
    });
  } catch (error) {
    res.status(500).json({
      error: "Failed to get circuit breaker status",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Health history endpoint
router.get("/health/history", authenticate, async (req, res) => {
  try {
    const history = HealthCheckService.getHealthHistory();
    res.json({
      timestamp: new Date().toISOString(),
      history,
      count: history.length,
    });
  } catch (error) {
    res.status(500).json({
      error: "Failed to get health history",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Queue management endpoints (admin only)
router.get("/queues/stats", authenticate, async (req, res) => {
  try {
    const stats = await EnhancedQueueService.getQueueStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to get queue statistics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

router.get("/queues/health", authenticate, async (req, res) => {
  try {
    const health = await EnhancedQueueService.healthCheck();
    res.json(health);
  } catch (error) {
    res.status(500).json({
      error: "Failed to check queue health",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

router.post("/queues/cleanup", authenticate, async (req, res) => {
  try {
    // Only allow admin users to cleanup queues
    if (req.user?.role !== "ADMIN") {
      return res.status(403).json({
        error: "Insufficient permissions",
        message: "Only admin users can cleanup queues",
      });
    }

    const { olderThanHours = 24 } = req.body;
    await EnhancedQueueService.cleanupJobs(olderThanHours);
    
    res.json({
      message: "Queue cleanup completed",
      olderThanHours,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      error: "Failed to cleanup queues",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Cache management endpoints
router.get("/cache/stats", authenticate, async (req, res) => {
  try {
    const stats = DistributedCacheService.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to get cache statistics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

router.post("/cache/clear", authenticate, async (req, res) => {
  try {
    // Only allow admin users to clear cache
    if (req.user?.role !== "ADMIN") {
      return res.status(403).json({
        error: "Insufficient permissions",
        message: "Only admin users can clear cache",
      });
    }

    const success = await DistributedCacheService.clear();
    
    if (success) {
      res.json({
        message: "Cache cleared successfully",
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        error: "Failed to clear cache",
      });
    }
  } catch (error) {
    res.status(500).json({
      error: "Failed to clear cache",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

router.post("/cache/warm-up", authenticate, async (req, res) => {
  try {
    // Only allow admin users to warm up cache
    if (req.user?.role !== "ADMIN") {
      return res.status(403).json({
        error: "Insufficient permissions",
        message: "Only admin users can warm up cache",
      });
    }

    await DistributedCacheService.warmUp();
    
    res.json({
      message: "Cache warm-up initiated",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      error: "Failed to warm up cache",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// WebSocket statistics endpoint
router.get("/websockets/stats", authenticate, async (req, res) => {
  try {
    const stats = getWebSocketStats();
    res.json({
      timestamp: new Date().toISOString(),
      ...stats,
    });
  } catch (error) {
    res.status(500).json({
      error: "Failed to get WebSocket statistics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Database statistics endpoint
router.get("/database/stats", authenticate, async (req, res) => {
  try {
    const stats = await getDatabaseMetrics();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to get database statistics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Redis statistics endpoint
router.get("/redis/stats", authenticate, async (req, res) => {
  try {
    const stats = await getRedisMetrics();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to get Redis statistics",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// Cluster information endpoint
router.get("/cluster/info", authenticate, async (req, res) => {
  try {
    const info = ProductionClusterManager.getClusterInfo();
    res.json({
      timestamp: new Date().toISOString(),
      ...info,
    });
  } catch (error) {
    res.status(500).json({
      error: "Failed to get cluster information",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

// System information endpoint
router.get("/system/info", async (req, res) => {
  try {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    const systemInfo = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      environment: process.env.NODE_ENV,
      versions: process.versions,
    };

    res.json(systemInfo);
  } catch (error) {
    res.status(500).json({
      error: "Failed to get system information",
      message: error instanceof Error ? error.message : String(error),
    });
  }
});

export default router;
