import Redis from "ioredis";
import { Logger } from "../services/loggerService";

// Redis configuration interface
interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  connectTimeout: number;
  commandTimeout: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
}

// Get Redis configuration from environment
const getRedisConfig = (): RedisConfig => {
  const redisUrl = process.env.REDIS_URL || "redis://localhost:6379";
  const url = new URL(redisUrl);

  return {
    host: url.hostname || "localhost",
    port: parseInt(url.port) || 6379,
    password: url.password || process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || "0"),
    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || "3"),
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || "1000"),
    connectTimeout: parseInt(process.env.REDIS_CONNECTION_TIMEOUT || "5000"),
    commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || "5000"),
    lazyConnect: true,
    keepAlive: 30000,
    family: 4, // IPv4
  };
};

// Create Redis client with enhanced configuration
const createRedisClient = (config: RedisConfig, clientName: string = "default") => {
  const client = new Redis({
    ...config,
    // Connection pool settings
    maxLoadingTimeout: 5000,
    // Retry strategy
    retryStrategy: (times: number) => {
      const delay = Math.min(times * 50, 2000);
      Logger.warn(`Redis ${clientName} connection retry attempt ${times}, delay: ${delay}ms`);
      return delay;
    },
    // Reconnect on error
    reconnectOnError: (err: Error) => {
      const targetError = "READONLY";
      return err.message.includes(targetError);
    },
  });

  // Event handlers
  client.on("connect", () => {
    Logger.info(`Redis ${clientName} client connected`);
  });

  client.on("ready", () => {
    Logger.info(`Redis ${clientName} client ready`);
  });

  client.on("error", (error) => {
    Logger.error(`Redis ${clientName} client error:`, error);
  });

  client.on("close", () => {
    Logger.warn(`Redis ${clientName} client connection closed`);
  });

  client.on("reconnecting", () => {
    Logger.info(`Redis ${clientName} client reconnecting...`);
  });

  return client;
};

// Redis configuration
const redisConfig = getRedisConfig();

// Create Redis clients for different purposes
export const redisClient = createRedisClient(redisConfig, "main");
export const redisCacheClient = createRedisClient(redisConfig, "cache");
export const redisQueueClient = createRedisClient(redisConfig, "queue");
export const redisPubSubClient = createRedisClient(redisConfig, "pubsub");

// Redis connection health check
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    const result = await redisClient.ping();
    return result === "PONG";
  } catch (error) {
    Logger.error("Redis health check failed:", error);
    return false;
  }
};

// Redis metrics collection
export const getRedisMetrics = async () => {
  try {
    const info = await redisClient.info();
    const memory = await redisClient.info("memory");
    const stats = await redisClient.info("stats");

    return {
      connected: true,
      info: {
        version: info.match(/redis_version:([^\r\n]+)/)?.[1] || "unknown",
        uptime: info.match(/uptime_in_seconds:([^\r\n]+)/)?.[1] || "0",
        connected_clients: info.match(/connected_clients:([^\r\n]+)/)?.[1] || "0",
      },
      memory: {
        used: memory.match(/used_memory_human:([^\r\n]+)/)?.[1] || "0B",
        peak: memory.match(/used_memory_peak_human:([^\r\n]+)/)?.[1] || "0B",
        fragmentation_ratio: memory.match(/mem_fragmentation_ratio:([^\r\n]+)/)?.[1] || "0",
      },
      stats: {
        total_commands_processed: stats.match(/total_commands_processed:([^\r\n]+)/)?.[1] || "0",
        total_connections_received: stats.match(/total_connections_received:([^\r\n]+)/)?.[1] || "0",
        keyspace_hits: stats.match(/keyspace_hits:([^\r\n]+)/)?.[1] || "0",
        keyspace_misses: stats.match(/keyspace_misses:([^\r\n]+)/)?.[1] || "0",
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    Logger.error("Failed to collect Redis metrics:", error);
    return {
      connected: false,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    };
  }
};

// Graceful Redis shutdown
export const gracefulRedisShutdown = async (): Promise<void> => {
  Logger.info("Shutting down Redis connections...");

  try {
    await Promise.all([
      redisClient.quit(),
      redisCacheClient.quit(),
      redisQueueClient.quit(),
      redisPubSubClient.quit(),
    ]);

    Logger.info("Redis connections closed successfully");
  } catch (error) {
    Logger.error("Error during Redis shutdown:", error);
    throw error;
  }
};

// Redis utility functions
export class RedisService {
  // Get with fallback
  static async get(key: string): Promise<string | null> {
    try {
      return await redisClient.get(key);
    } catch (error) {
      Logger.error(`Redis GET error for key ${key}:`, error);
      return null;
    }
  }

  // Set with TTL
  static async set(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      if (ttl) {
        await redisClient.setex(key, ttl, value);
      } else {
        await redisClient.set(key, value);
      }
      return true;
    } catch (error) {
      Logger.error(`Redis SET error for key ${key}:`, error);
      return false;
    }
  }

  // Delete key
  static async del(key: string): Promise<boolean> {
    try {
      const result = await redisClient.del(key);
      return result > 0;
    } catch (error) {
      Logger.error(`Redis DEL error for key ${key}:`, error);
      return false;
    }
  }

  // Check if key exists
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      Logger.error(`Redis EXISTS error for key ${key}:`, error);
      return false;
    }
  }

  // Increment counter
  static async incr(key: string): Promise<number | null> {
    try {
      return await redisClient.incr(key);
    } catch (error) {
      Logger.error(`Redis INCR error for key ${key}:`, error);
      return null;
    }
  }

  // Set expiration
  static async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await redisClient.expire(key, seconds);
      return result === 1;
    } catch (error) {
      Logger.error(`Redis EXPIRE error for key ${key}:`, error);
      return false;
    }
  }

  // Get multiple keys
  static async mget(keys: string[]): Promise<(string | null)[]> {
    try {
      return await redisClient.mget(...keys);
    } catch (error) {
      Logger.error(`Redis MGET error for keys ${keys.join(", ")}:`, error);
      return new Array(keys.length).fill(null);
    }
  }

  // Set multiple keys
  static async mset(keyValues: Record<string, string>): Promise<boolean> {
    try {
      const args = Object.entries(keyValues).flat();
      await redisClient.mset(...args);
      return true;
    } catch (error) {
      Logger.error("Redis MSET error:", error);
      return false;
    }
  }
}

// Initialize Redis connections
export const initializeRedis = async (): Promise<void> => {
  try {
    Logger.info("Initializing Redis connections...");
    
    // Test connections
    await Promise.all([
      redisClient.ping(),
      redisCacheClient.ping(),
      redisQueueClient.ping(),
      redisPubSubClient.ping(),
    ]);

    Logger.info("All Redis connections initialized successfully");
  } catch (error) {
    Logger.error("Failed to initialize Redis connections:", error);
    throw error;
  }
};

export default {
  redisClient,
  redisCacheClient,
  redisQueueClient,
  redisPubSubClient,
  RedisService,
  checkRedisHealth,
  getRedisMetrics,
  gracefulRedisShutdown,
  initializeRedis,
};
