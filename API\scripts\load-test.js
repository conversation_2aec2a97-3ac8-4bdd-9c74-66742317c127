#!/usr/bin/env node

/**
 * Load testing script for IonAlumni API
 * Tests the application's ability to handle 10K+ concurrent users
 */

const http = require('http');
const WebSocket = require('ws');
const cluster = require('cluster');
const os = require('os');

// Test configuration
const CONFIG = {
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  wsUrl: process.env.WS_TEST_URL || 'ws://localhost:3001',
  totalUsers: parseInt(process.env.TOTAL_USERS || '10000'),
  rampUpTime: parseInt(process.env.RAMP_UP_TIME || '60'), // seconds
  testDuration: parseInt(process.env.TEST_DURATION || '300'), // seconds
  workers: parseInt(process.env.WORKERS || os.cpus().length),
  endpoints: [
    { path: '/health', weight: 10 },
    { path: '/api/auth/login', weight: 5, method: 'POST' },
    { path: '/api/users', weight: 20 },
    { path: '/api/monitoring/stats', weight: 5 },
  ],
};

// Statistics tracking
let stats = {
  requests: 0,
  responses: 0,
  errors: 0,
  wsConnections: 0,
  wsErrors: 0,
  responseTimes: [],
  statusCodes: {},
  startTime: Date.now(),
};

// Test user data
const testUsers = Array.from({ length: CONFIG.totalUsers }, (_, i) => ({
  id: i + 1,
  email: `user${i + 1}@test.com`,
  password: 'testpassword123',
}));

class LoadTester {
  constructor(workerId) {
    this.workerId = workerId;
    this.usersPerWorker = Math.ceil(CONFIG.totalUsers / CONFIG.workers);
    this.startIndex = workerId * this.usersPerWorker;
    this.endIndex = Math.min(this.startIndex + this.usersPerWorker, CONFIG.totalUsers);
    this.activeConnections = new Set();
    this.testRunning = false;
  }

  // HTTP request helper
  makeRequest(options, data = null) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const req = http.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          const endTime = Date.now();
          const responseTime = endTime - startTime;
          
          stats.responses++;
          stats.responseTimes.push(responseTime);
          stats.statusCodes[res.statusCode] = (stats.statusCodes[res.statusCode] || 0) + 1;
          
          resolve({
            statusCode: res.statusCode,
            responseTime,
            data: responseData,
          });
        });
      });
      
      req.on('error', (error) => {
        stats.errors++;
        resolve({
          error: error.message,
          responseTime: Date.now() - startTime,
        });
      });
      
      req.setTimeout(30000, () => {
        req.destroy();
        stats.errors++;
        resolve({
          error: 'Request timeout',
          responseTime: 30000,
        });
      });
      
      if (data) {
        req.write(JSON.stringify(data));
      }
      
      req.end();
      stats.requests++;
    });
  }

  // WebSocket connection helper
  createWebSocketConnection(userId) {
    return new Promise((resolve) => {
      const ws = new WebSocket(CONFIG.wsUrl);
      
      ws.on('open', () => {
        stats.wsConnections++;
        this.activeConnections.add(ws);
        
        // Authenticate
        ws.send(JSON.stringify({
          type: 'auth',
          token: `test-token-${userId}`,
        }));
        
        resolve(ws);
      });
      
      ws.on('error', (error) => {
        stats.wsErrors++;
        resolve(null);
      });
      
      ws.on('close', () => {
        this.activeConnections.delete(ws);
      });
      
      // Keep connection alive with pings
      const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ type: 'ping' }));
        } else {
          clearInterval(pingInterval);
        }
      }, 30000);
    });
  }

  // Simulate user behavior
  async simulateUser(userId) {
    const user = testUsers[userId - 1];
    
    // Create WebSocket connection
    const ws = await this.createWebSocketConnection(userId);
    
    // Simulate HTTP requests
    const requestInterval = setInterval(async () => {
      if (!this.testRunning) {
        clearInterval(requestInterval);
        return;
      }
      
      // Select random endpoint based on weight
      const endpoint = this.selectRandomEndpoint();
      const url = new URL(endpoint.path, CONFIG.baseUrl);
      
      const options = {
        hostname: url.hostname,
        port: url.port || 80,
        path: url.pathname,
        method: endpoint.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `LoadTest-Worker-${this.workerId}-User-${userId}`,
        },
      };
      
      let data = null;
      if (endpoint.method === 'POST' && endpoint.path.includes('login')) {
        data = {
          email: user.email,
          password: user.password,
        };
      }
      
      await this.makeRequest(options, data);
    }, Math.random() * 5000 + 1000); // Random interval between 1-6 seconds
    
    // Simulate WebSocket messages
    if (ws) {
      const messageInterval = setInterval(() => {
        if (!this.testRunning || ws.readyState !== WebSocket.OPEN) {
          clearInterval(messageInterval);
          return;
        }
        
        ws.send(JSON.stringify({
          type: 'send_message',
          receiverId: `user${Math.floor(Math.random() * CONFIG.totalUsers) + 1}`,
          content: `Test message from user ${userId}`,
          messageId: `msg-${Date.now()}-${userId}`,
        }));
      }, Math.random() * 10000 + 5000); // Random interval between 5-15 seconds
    }
  }

  // Select random endpoint based on weight
  selectRandomEndpoint() {
    const totalWeight = CONFIG.endpoints.reduce((sum, ep) => sum + ep.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const endpoint of CONFIG.endpoints) {
      random -= endpoint.weight;
      if (random <= 0) {
        return endpoint;
      }
    }
    
    return CONFIG.endpoints[0];
  }

  // Start load test for this worker
  async startTest() {
    console.log(`Worker ${this.workerId}: Starting test for users ${this.startIndex + 1} to ${this.endIndex}`);
    
    this.testRunning = true;
    
    // Ramp up users gradually
    const rampUpInterval = (CONFIG.rampUpTime * 1000) / (this.endIndex - this.startIndex);
    
    for (let i = this.startIndex; i < this.endIndex; i++) {
      if (!this.testRunning) break;
      
      setTimeout(() => {
        this.simulateUser(i + 1);
      }, (i - this.startIndex) * rampUpInterval);
    }
    
    // Stop test after duration
    setTimeout(() => {
      this.stopTest();
    }, (CONFIG.rampUpTime + CONFIG.testDuration) * 1000);
  }

  // Stop load test
  stopTest() {
    console.log(`Worker ${this.workerId}: Stopping test`);
    this.testRunning = false;
    
    // Close all WebSocket connections
    this.activeConnections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });
    
    // Send stats to master
    if (process.send) {
      process.send({
        type: 'stats',
        workerId: this.workerId,
        stats: { ...stats },
      });
    }
  }
}

// Master process
if (cluster.isMaster) {
  console.log(`Starting load test with ${CONFIG.totalUsers} users across ${CONFIG.workers} workers`);
  console.log(`Target: ${CONFIG.baseUrl}`);
  console.log(`WebSocket: ${CONFIG.wsUrl}`);
  console.log(`Ramp up: ${CONFIG.rampUpTime}s, Duration: ${CONFIG.testDuration}s`);
  console.log('---');
  
  const workerStats = [];
  let completedWorkers = 0;
  
  // Fork workers
  for (let i = 0; i < CONFIG.workers; i++) {
    const worker = cluster.fork({ WORKER_ID: i });
    
    worker.on('message', (message) => {
      if (message.type === 'stats') {
        workerStats.push(message.stats);
        completedWorkers++;
        
        if (completedWorkers === CONFIG.workers) {
          generateReport(workerStats);
        }
      }
    });
  }
  
  // Handle worker exit
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
  });
  
} else {
  // Worker process
  const workerId = parseInt(process.env.WORKER_ID);
  const tester = new LoadTester(workerId);
  tester.startTest();
}

// Generate test report
function generateReport(workerStats) {
  const combined = workerStats.reduce((acc, stats) => {
    acc.requests += stats.requests;
    acc.responses += stats.responses;
    acc.errors += stats.errors;
    acc.wsConnections += stats.wsConnections;
    acc.wsErrors += stats.wsErrors;
    acc.responseTimes.push(...stats.responseTimes);
    
    Object.keys(stats.statusCodes).forEach(code => {
      acc.statusCodes[code] = (acc.statusCodes[code] || 0) + stats.statusCodes[code];
    });
    
    return acc;
  }, {
    requests: 0,
    responses: 0,
    errors: 0,
    wsConnections: 0,
    wsErrors: 0,
    responseTimes: [],
    statusCodes: {},
  });
  
  // Calculate statistics
  const totalTime = (Date.now() - stats.startTime) / 1000;
  const rps = combined.responses / totalTime;
  const errorRate = (combined.errors / combined.requests) * 100;
  
  combined.responseTimes.sort((a, b) => a - b);
  const p50 = combined.responseTimes[Math.floor(combined.responseTimes.length * 0.5)];
  const p95 = combined.responseTimes[Math.floor(combined.responseTimes.length * 0.95)];
  const p99 = combined.responseTimes[Math.floor(combined.responseTimes.length * 0.99)];
  const avgResponseTime = combined.responseTimes.reduce((a, b) => a + b, 0) / combined.responseTimes.length;
  
  console.log('\n=== LOAD TEST RESULTS ===');
  console.log(`Total Duration: ${totalTime.toFixed(2)}s`);
  console.log(`Total Requests: ${combined.requests}`);
  console.log(`Total Responses: ${combined.responses}`);
  console.log(`Total Errors: ${combined.errors}`);
  console.log(`Error Rate: ${errorRate.toFixed(2)}%`);
  console.log(`Requests/sec: ${rps.toFixed(2)}`);
  console.log(`WebSocket Connections: ${combined.wsConnections}`);
  console.log(`WebSocket Errors: ${combined.wsErrors}`);
  console.log('\n=== RESPONSE TIMES ===');
  console.log(`Average: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`50th percentile: ${p50}ms`);
  console.log(`95th percentile: ${p95}ms`);
  console.log(`99th percentile: ${p99}ms`);
  console.log('\n=== STATUS CODES ===');
  Object.keys(combined.statusCodes).forEach(code => {
    console.log(`${code}: ${combined.statusCodes[code]}`);
  });
  
  // Performance assessment
  console.log('\n=== PERFORMANCE ASSESSMENT ===');
  if (p95 < 100) {
    console.log('✅ Excellent performance (p95 < 100ms)');
  } else if (p95 < 500) {
    console.log('✅ Good performance (p95 < 500ms)');
  } else if (p95 < 1000) {
    console.log('⚠️  Acceptable performance (p95 < 1000ms)');
  } else {
    console.log('❌ Poor performance (p95 > 1000ms)');
  }
  
  if (errorRate < 1) {
    console.log('✅ Low error rate (< 1%)');
  } else if (errorRate < 5) {
    console.log('⚠️  Moderate error rate (< 5%)');
  } else {
    console.log('❌ High error rate (> 5%)');
  }
  
  if (rps > 1000) {
    console.log('✅ High throughput (> 1000 RPS)');
  } else if (rps > 500) {
    console.log('✅ Good throughput (> 500 RPS)');
  } else {
    console.log('⚠️  Low throughput (< 500 RPS)');
  }
  
  process.exit(0);
}
